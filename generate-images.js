const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Image definitions for all required images
const imageDefinitions = [
    // Category Images (280x350)
    { filename: 'category-casual.jpg', text: 'CASUAL\nCOLLECTION', color: '#667eea', width: 280, height: 350 },
    { filename: 'category-urban.jpg', text: 'URBAN\nSTYLE', color: '#764ba2', width: 280, height: 350 },
    { filename: 'category-sporty.jpg', text: 'SPORTY\nFIT', color: '#f093fb', width: 280, height: 350 },
    { filename: 'category-oversized.jpg', text: 'OVERSIZED\nCOMFORT', color: '#f5576c', width: 280, height: 350 },
    { filename: 'category-premium.jpg', text: 'PREMIUM\nQUALITY', color: '#4facfe', width: 280, height: 350 },
    
    // Product Images (300x300)
    { filename: 'product-1.jpg', text: 'ESSENTIAL\nWHITE TEE', color: '#ffffff', textColor: '#000', width: 300, height: 300 },
    { filename: 'product-2.jpg', text: 'CLASSIC\nBLACK TEE', color: '#000000', width: 300, height: 300 },
    { filename: 'product-3.jpg', text: 'VINTAGE\nGRAY TEE', color: '#808080', width: 300, height: 300 },
    { filename: 'product-4.jpg', text: 'PREMIUM\nNAVY TEE', color: '#000080', width: 300, height: 300 },
    { filename: 'product-5.jpg', text: 'OVERSIZED\nCREAM TEE', color: '#F5F5DC', textColor: '#000', width: 300, height: 300 },
    { filename: 'product-6.jpg', text: 'MINIMALIST\nOLIVE TEE', color: '#808000', width: 300, height: 300 },
    { filename: 'product-7.jpg', text: 'URBAN\nCHARCOAL TEE', color: '#36454F', width: 300, height: 300 },
    { filename: 'product-8.jpg', text: 'CLASSIC\nBURGUNDY TEE', color: '#800020', width: 300, height: 300 },
    
    // Instagram Images (300x300)
    { filename: 'instagram-1.jpg', text: '#WEARROOT\nSTYLE', color: '#667eea', width: 300, height: 300 },
    { filename: 'instagram-2.jpg', text: '#WEARROOT\nCASUAL', color: '#764ba2', width: 300, height: 300 },
    { filename: 'instagram-3.jpg', text: '#WEARROOT\nURBAN', color: '#f093fb', width: 300, height: 300 },
    { filename: 'instagram-4.jpg', text: '#WEARROOT\nPREMIUM', color: '#f5576c', width: 300, height: 300 },
    { filename: 'instagram-5.jpg', text: '#WEARROOT\nCOMFORT', color: '#4facfe', width: 300, height: 300 },
    { filename: 'instagram-6.jpg', text: '#WEARROOT\nQUALITY', color: '#667eea', width: 300, height: 300 },
    
    // Avatar Images (50x50)
    { filename: 'avatar-1.jpg', text: 'AJ', color: '#667eea', width: 50, height: 50, fontSize: 16 },
    { filename: 'avatar-2.jpg', text: 'MC', color: '#764ba2', width: 50, height: 50, fontSize: 16 },
    { filename: 'avatar-3.jpg', text: 'DR', color: '#f093fb', width: 50, height: 50, fontSize: 16 },
    
    // Brand Story (600x400)
    { filename: 'brand-story.jpg', text: 'ROOT BRAND\nSTORY', color: '#667eea', width: 600, height: 400 }
];

function hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

function generateImage(imgDef) {
    const canvas = createCanvas(imgDef.width, imgDef.height);
    const ctx = canvas.getContext('2d');
    
    // Create gradient background
    const rgb = hexToRgb(imgDef.color);
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`);
    gradient.addColorStop(1, `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.5)`);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Add text
    ctx.fillStyle = imgDef.textColor || '#ffffff';
    const fontSize = imgDef.fontSize || Math.max(16, Math.min(canvas.width, canvas.height) / 15);
    ctx.font = `bold ${fontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const lines = imgDef.text.split('\n');
    const lineHeight = fontSize * 1.2;
    const startY = canvas.height / 2 - (lines.length - 1) * lineHeight / 2;
    
    lines.forEach((line, index) => {
        ctx.fillText(line, canvas.width / 2, startY + index * lineHeight);
    });
    
    // Add ROOT logo (except for avatars)
    if (imgDef.width > 100) {
        ctx.font = `bold ${Math.max(12, fontSize * 0.7)}px Arial`;
        ctx.fillStyle = imgDef.textColor || '#ffffff';
        ctx.fillText('ROOT', canvas.width - 40, 30);
    }
    
    return canvas;
}

async function generateAllImages() {
    const outputDir = path.join(__dirname, 'src', 'assets', 'images');
    
    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    
    console.log('Generating placeholder images...');
    
    for (const imgDef of imageDefinitions) {
        try {
            const canvas = generateImage(imgDef);
            const buffer = canvas.toBuffer('image/jpeg', { quality: 0.9 });
            const filePath = path.join(outputDir, imgDef.filename);
            
            fs.writeFileSync(filePath, buffer);
            console.log(`✓ Generated ${imgDef.filename}`);
        } catch (error) {
            console.error(`✗ Failed to generate ${imgDef.filename}:`, error.message);
        }
    }
    
    console.log(`\nGenerated ${imageDefinitions.length} placeholder images in ${outputDir}`);
}

// Run the generator
if (require.main === module) {
    generateAllImages().catch(console.error);
}

module.exports = { generateAllImages, imageDefinitions };
