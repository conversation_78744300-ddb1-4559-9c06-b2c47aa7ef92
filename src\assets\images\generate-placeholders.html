<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Placeholder Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .controls {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .image-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .image-item h3 {
            margin-top: 0;
            color: #333;
        }
        .image-item canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .download-btn:hover {
            background: #218838;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ROOT T-Shirt Brand - Image Generator</h1>
            <p>Generate and download all placeholder images for the ROOT website</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="generateAllImages()">Generate All Images</button>
            <button class="btn" onclick="downloadAllImages()">Download All Images</button>
        </div>

        <div class="status" id="status">
            Click "Generate All Images" to create placeholder images
        </div>

        <div class="grid" id="imageGrid">
            <!-- Images will be generated here -->
        </div>
    </div>

    <script>
        // Image definitions for all required images
        const imageDefinitions = [
            // Category Images (280x350)
            { id: 'category-casual', filename: 'category-casual.jpg', text: 'CASUAL\nCOLLECTION', color: '#667eea', width: 280, height: 350 },
            { id: 'category-urban', filename: 'category-urban.jpg', text: 'URBAN\nSTYLE', color: '#764ba2', width: 280, height: 350 },
            { id: 'category-sporty', filename: 'category-sporty.jpg', text: 'SPORTY\nFIT', color: '#f093fb', width: 280, height: 350 },
            { id: 'category-oversized', filename: 'category-oversized.jpg', text: 'OVERSIZED\nCOMFORT', color: '#f5576c', width: 280, height: 350 },
            { id: 'category-premium', filename: 'category-premium.jpg', text: 'PREMIUM\nQUALITY', color: '#4facfe', width: 280, height: 350 },

            // Product Images (300x300)
            { id: 'product-1', filename: 'product-1.jpg', text: 'ESSENTIAL\nWHITE TEE', color: '#ffffff', textColor: '#000', width: 300, height: 300 },
            { id: 'product-2', filename: 'product-2.jpg', text: 'CLASSIC\nBLACK TEE', color: '#000000', width: 300, height: 300 },
            { id: 'product-3', filename: 'product-3.jpg', text: 'VINTAGE\nGRAY TEE', color: '#808080', width: 300, height: 300 },
            { id: 'product-4', filename: 'product-4.jpg', text: 'PREMIUM\nNAVY TEE', color: '#000080', width: 300, height: 300 },
            { id: 'product-5', filename: 'product-5.jpg', text: 'OVERSIZED\nCREAM TEE', color: '#F5F5DC', textColor: '#000', width: 300, height: 300 },
            { id: 'product-6', filename: 'product-6.jpg', text: 'MINIMALIST\nOLIVE TEE', color: '#808000', width: 300, height: 300 },
            { id: 'product-7', filename: 'product-7.jpg', text: 'URBAN\nCHARCOAL TEE', color: '#36454F', width: 300, height: 300 },
            { id: 'product-8', filename: 'product-8.jpg', text: 'CLASSIC\nBURGUNDY TEE', color: '#800020', width: 300, height: 300 },

            // Instagram Images (300x300)
            { id: 'instagram-1', filename: 'instagram-1.jpg', text: '#WEARROOT\nSTYLE', color: '#667eea', width: 300, height: 300 },
            { id: 'instagram-2', filename: 'instagram-2.jpg', text: '#WEARROOT\nCASUAL', color: '#764ba2', width: 300, height: 300 },
            { id: 'instagram-3', filename: 'instagram-3.jpg', text: '#WEARROOT\nURBAN', color: '#f093fb', width: 300, height: 300 },
            { id: 'instagram-4', filename: 'instagram-4.jpg', text: '#WEARROOT\nPREMIUM', color: '#f5576c', width: 300, height: 300 },
            { id: 'instagram-5', filename: 'instagram-5.jpg', text: '#WEARROOT\nCOMFORT', color: '#4facfe', width: 300, height: 300 },
            { id: 'instagram-6', filename: 'instagram-6.jpg', text: '#WEARROOT\nQUALITY', color: '#667eea', width: 300, height: 300 },

            // Avatar Images (50x50)
            { id: 'avatar-1', filename: 'avatar-1.jpg', text: 'AJ', color: '#667eea', width: 50, height: 50, fontSize: 16 },
            { id: 'avatar-2', filename: 'avatar-2.jpg', text: 'MC', color: '#764ba2', width: 50, height: 50, fontSize: 16 },
            { id: 'avatar-3', filename: 'avatar-3.jpg', text: 'DR', color: '#f093fb', width: 50, height: 50, fontSize: 16 },

            // Brand Story (600x400)
            { id: 'brand-story', filename: 'brand-story.jpg', text: 'ROOT BRAND\nSTORY', color: '#667eea', width: 600, height: 400 }
        ];

        let generatedImages = {};

        function generateAllImages() {
            const grid = document.getElementById('imageGrid');
            const status = document.getElementById('status');

            grid.innerHTML = '';
            generatedImages = {};

            status.innerHTML = 'Generating images...';

            imageDefinitions.forEach((imgDef, index) => {
                setTimeout(() => {
                    generateImage(imgDef);
                    if (index === imageDefinitions.length - 1) {
                        status.innerHTML = `Generated ${imageDefinitions.length} images successfully! Click individual download buttons or "Download All Images".`;
                    }
                }, index * 100);
            });
        }

        function generateImage(imgDef) {
            // Create container
            const container = document.createElement('div');
            container.className = 'image-item';

            // Create canvas
            const canvas = document.createElement('canvas');
            canvas.id = imgDef.id;
            canvas.width = imgDef.width;
            canvas.height = imgDef.height;

            const ctx = canvas.getContext('2d');

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, imgDef.color);
            gradient.addColorStop(1, imgDef.color + '80');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add text
            ctx.fillStyle = imgDef.textColor || '#ffffff';
            const fontSize = imgDef.fontSize || Math.max(16, Math.min(canvas.width, canvas.height) / 15);
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const lines = imgDef.text.split('\n');
            const lineHeight = fontSize * 1.2;
            const startY = canvas.height / 2 - (lines.length - 1) * lineHeight / 2;

            lines.forEach((line, index) => {
                ctx.fillText(line, canvas.width / 2, startY + index * lineHeight);
            });

            // Add ROOT logo (except for avatars)
            if (imgDef.width > 100) {
                ctx.font = `bold ${Math.max(12, fontSize * 0.7)}px Arial`;
                ctx.fillStyle = imgDef.textColor || '#ffffff';
                ctx.fillText('ROOT', canvas.width - 40, 30);
            }

            // Store canvas data
            generatedImages[imgDef.filename] = canvas.toDataURL('image/jpeg', 0.9);

            // Create UI
            container.innerHTML = `
                <h3>${imgDef.filename}</h3>
                <div>${canvas.outerHTML}</div>
                <button class="download-btn" onclick="downloadImage('${imgDef.filename}')">Download</button>
            `;

            document.getElementById('imageGrid').appendChild(container);
        }

        function downloadImage(filename) {
            if (!generatedImages[filename]) {
                alert('Image not generated yet!');
                return;
            }

            const link = document.createElement('a');
            link.download = filename;
            link.href = generatedImages[filename];
            link.click();
        }

        function downloadAllImages() {
            if (Object.keys(generatedImages).length === 0) {
                alert('Please generate images first!');
                return;
            }

            Object.keys(generatedImages).forEach((filename, index) => {
                setTimeout(() => {
                    downloadImage(filename);
                }, index * 200);
            });
        }
    </script>
</body>
</html>
