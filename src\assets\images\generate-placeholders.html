<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generate Placeholder Images</title>
</head>
<body>
    <h1>ROOT T-Shirt Brand - Placeholder Image Generator</h1>
    <p>This page generates placeholder images for the ROOT website. Right-click and save each image.</p>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; padding: 20px;">

        <!-- Category Images -->
        <div>
            <h3>Categories</h3>
            <canvas id="category-casual" width="280" height="350"></canvas>
            <p>category-casual.jpg</p>
        </div>

        <div>
            <canvas id="category-urban" width="280" height="350"></canvas>
            <p>category-urban.jpg</p>
        </div>

        <div>
            <canvas id="category-sporty" width="280" height="350"></canvas>
            <p>category-sporty.jpg</p>
        </div>

        <div>
            <canvas id="category-oversized" width="280" height="350"></canvas>
            <p>category-oversized.jpg</p>
        </div>

        <div>
            <canvas id="category-premium" width="280" height="350"></canvas>
            <p>category-premium.jpg</p>
        </div>

        <!-- Product Images -->
        <div>
            <h3>Products</h3>
            <canvas id="product-1" width="300" height="300"></canvas>
            <p>product-1.jpg</p>
        </div>

        <div>
            <canvas id="product-2" width="300" height="300"></canvas>
            <p>product-2.jpg</p>
        </div>

        <div>
            <canvas id="product-3" width="300" height="300"></canvas>
            <p>product-3.jpg</p>
        </div>

        <div>
            <canvas id="product-4" width="300" height="300"></canvas>
            <p>product-4.jpg</p>
        </div>

        <!-- Brand Story -->
        <div>
            <h3>Brand Story</h3>
            <canvas id="brand-story" width="600" height="400"></canvas>
            <p>brand-story.jpg</p>
        </div>

        <!-- Team Members -->
        <div>
            <h3>Team</h3>
            <canvas id="team-alex" width="200" height="200"></canvas>
            <p>team-alex.jpg</p>
        </div>

        <div>
            <canvas id="team-sarah" width="200" height="200"></canvas>
            <p>team-sarah.jpg</p>
        </div>

    </div>

    <script>
        // Generate placeholder images
        const images = [
            { id: 'category-casual', text: 'CASUAL\nCOLLECTION', color: '#667eea' },
            { id: 'category-urban', text: 'URBAN\nSTYLE', color: '#764ba2' },
            { id: 'category-sporty', text: 'SPORTY\nFIT', color: '#f093fb' },
            { id: 'category-oversized', text: 'OVERSIZED\nCOMFORT', color: '#f5576c' },
            { id: 'category-premium', text: 'PREMIUM\nQUALITY', color: '#4facfe' },
            { id: 'product-1', text: 'ESSENTIAL\nWHITE TEE', color: '#ffffff', textColor: '#000' },
            { id: 'product-2', text: 'CLASSIC\nBLACK TEE', color: '#000000' },
            { id: 'product-3', text: 'VINTAGE\nGRAY TEE', color: '#808080' },
            { id: 'product-4', text: 'PREMIUM\nNAVY TEE', color: '#000080' },
            { id: 'brand-story', text: 'ROOT BRAND\nSTORY', color: '#667eea' },
            { id: 'team-alex', text: 'ALEX\nCHEN', color: '#764ba2' },
            { id: 'team-sarah', text: 'SARAH\nJOHNSON', color: '#f093fb' }
        ];

        images.forEach(img => {
            const canvas = document.getElementById(img.id);
            const ctx = canvas.getContext('2d');

            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, img.color);
            gradient.addColorStop(1, img.color + '80');

            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add text
            ctx.fillStyle = img.textColor || '#ffffff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const lines = img.text.split('\n');
            const lineHeight = 30;
            const startY = canvas.height / 2 - (lines.length - 1) * lineHeight / 2;

            lines.forEach((line, index) => {
                ctx.fillText(line, canvas.width / 2, startY + index * lineHeight);
            });

            // Add ROOT logo
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = img.textColor || '#ffffff';
            ctx.fillText('ROOT', canvas.width - 40, 30);
        });
    </script>
</body>
</html>
