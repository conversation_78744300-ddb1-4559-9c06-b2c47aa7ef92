<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Images - ROOT T-Shirt Brand</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 40px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .image-item {
            text-align: center;
        }
        .image-item img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .image-item h4 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ROOT T-Shirt Brand - Image Test</h1>
            <p>Testing all placeholder images for the website</p>
        </div>

        <div class="section">
            <h2>Category Images (280x350)</h2>
            <div class="grid">
                <div class="image-item">
                    <h4>Casual Collection</h4>
                    <img src="src/assets/images/category-casual.jpg" alt="Casual Collection" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-category-casual">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Urban Style</h4>
                    <img src="src/assets/images/category-urban.jpg" alt="Urban Style" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-category-urban">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Sporty Fit</h4>
                    <img src="src/assets/images/category-sporty.jpg" alt="Sporty Fit" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-category-sporty">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Oversized Comfort</h4>
                    <img src="src/assets/images/category-oversized.jpg" alt="Oversized Comfort" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-category-oversized">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Premium Quality</h4>
                    <img src="src/assets/images/category-premium.jpg" alt="Premium Quality" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-category-premium">Loading...</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Product Images (300x300)</h2>
            <div class="grid">
                <div class="image-item">
                    <h4>Essential White Tee</h4>
                    <img src="src/assets/images/product-1.jpg" alt="Essential White Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-1">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Classic Black Tee</h4>
                    <img src="src/assets/images/product-2.jpg" alt="Classic Black Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-2">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Vintage Gray Tee</h4>
                    <img src="src/assets/images/product-3.jpg" alt="Vintage Gray Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-3">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Premium Navy Tee</h4>
                    <img src="src/assets/images/product-4.jpg" alt="Premium Navy Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-4">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Oversized Cream Tee</h4>
                    <img src="src/assets/images/product-5.jpg" alt="Oversized Cream Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-5">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Minimalist Olive Tee</h4>
                    <img src="src/assets/images/product-6.jpg" alt="Minimalist Olive Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-6">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Urban Charcoal Tee</h4>
                    <img src="src/assets/images/product-7.jpg" alt="Urban Charcoal Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-7">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Classic Burgundy Tee</h4>
                    <img src="src/assets/images/product-8.jpg" alt="Classic Burgundy Tee" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-product-8">Loading...</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Instagram Images (300x300)</h2>
            <div class="grid">
                <div class="image-item">
                    <h4>Instagram Post 1</h4>
                    <img src="src/assets/images/instagram-1.jpg" alt="Instagram Post 1" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-instagram-1">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Instagram Post 2</h4>
                    <img src="src/assets/images/instagram-2.jpg" alt="Instagram Post 2" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-instagram-2">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Instagram Post 3</h4>
                    <img src="src/assets/images/instagram-3.jpg" alt="Instagram Post 3" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-instagram-3">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Instagram Post 4</h4>
                    <img src="src/assets/images/instagram-4.jpg" alt="Instagram Post 4" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-instagram-4">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Instagram Post 5</h4>
                    <img src="src/assets/images/instagram-5.jpg" alt="Instagram Post 5" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-instagram-5">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Instagram Post 6</h4>
                    <img src="src/assets/images/instagram-6.jpg" alt="Instagram Post 6" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-instagram-6">Loading...</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Avatar Images (50x50)</h2>
            <div class="grid">
                <div class="image-item">
                    <h4>Avatar 1</h4>
                    <img src="src/assets/images/avatar-1.jpg" alt="Avatar 1" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-avatar-1">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Avatar 2</h4>
                    <img src="src/assets/images/avatar-2.jpg" alt="Avatar 2" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-avatar-2">Loading...</div>
                </div>
                <div class="image-item">
                    <h4>Avatar 3</h4>
                    <img src="src/assets/images/avatar-3.jpg" alt="Avatar 3" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-avatar-3">Loading...</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Brand Story Image (600x400)</h2>
            <div class="grid">
                <div class="image-item">
                    <h4>Brand Story</h4>
                    <img src="src/assets/images/brand-story.jpg" alt="Brand Story" onload="markSuccess(this)" onerror="markError(this)">
                    <div class="status" id="status-brand-story">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function markSuccess(img) {
            const filename = img.src.split('/').pop().split('.')[0];
            const statusElement = document.getElementById('status-' + filename);
            if (statusElement) {
                statusElement.textContent = 'Loaded Successfully';
                statusElement.className = 'status success';
            }
        }

        function markError(img) {
            const filename = img.src.split('/').pop().split('.')[0];
            const statusElement = document.getElementById('status-' + filename);
            if (statusElement) {
                statusElement.textContent = 'Failed to Load';
                statusElement.className = 'status error';
            }
        }

        // Summary after all images are processed
        setTimeout(() => {
            const successCount = document.querySelectorAll('.status.success').length;
            const errorCount = document.querySelectorAll('.status.error').length;
            const totalCount = successCount + errorCount;
            
            console.log(`Image loading summary: ${successCount}/${totalCount} images loaded successfully`);
            
            if (errorCount === 0) {
                alert(`✅ All ${totalCount} images loaded successfully!`);
            } else {
                alert(`⚠️ ${errorCount} images failed to load. Check the console for details.`);
            }
        }, 3000);
    </script>
</body>
</html>
